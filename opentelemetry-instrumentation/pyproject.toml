[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "opentelemetry-instrumentation"
dynamic = ["version"]
description = "Instrumentation Tools & Auto Instrumentation for OpenTelemetry Python"
readme = "README.rst"
license = "Apache-2.0"
requires-python = ">=3.8"
authors = [
  { name = "OpenTelemetry Authors", email = "<EMAIL>" },
]
classifiers = [
  "Development Status :: 4 - Beta",
  "Intended Audience :: Developers",
  "License :: OSI Approved :: Apache Software License",
  "Programming Language :: Python",
  "Programming Language :: Python :: 3",
  "Programming Language :: Python :: 3.8",
  "Programming Language :: Python :: 3.9",
  "Programming Language :: Python :: 3.10",
  "Programming Language :: Python :: 3.11",
]
dependencies = [
  "opentelemetry-api ~= 1.4",
  "setuptools >= 16.0",
  "wrapt >= 1.0.0, < 2.0.0",
]

[project.scripts]
opentelemetry-bootstrap = "opentelemetry.instrumentation.bootstrap:run"
opentelemetry-instrument = "opentelemetry.instrumentation.auto_instrumentation:run"

[project.entry-points.opentelemetry_environment_variables]
instrumentation = "opentelemetry.instrumentation.environment_variables"

[project.urls]
Homepage = "https://github.com/open-telemetry/opentelemetry-python-contrib/tree/main/opentelemetry-instrumentation"

[tool.hatch.version]
path = "src/opentelemetry/instrumentation/version.py"

[tool.hatch.build.targets.sdist]
include = [
  "/src",
  "/tests",
]

[tool.hatch.build.targets.wheel]
packages = ["src/opentelemetry"]
