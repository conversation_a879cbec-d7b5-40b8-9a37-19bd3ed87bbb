# These will be sorted first in that order.
# All packages that are depended upon by others should be listed here.
[DEFAULT]
ignore=
    _template

sortfirst=
    opentelemetry-instrumentation
    util/opentelemetry-util-http
    instrumentation/opentelemetry-instrumentation-wsgi
    instrumentation/opentelemetry-instrumentation-dbapi
    instrumentation/opentelemetry-instrumentation-asgi
    instrumentation/opentelemetry-instrumentation-botocore
    instrumentation/*
    exporter/*
    ext/*

[stable]
version=1.24.0

packages=
    opentelemetry-sdk
    opentelemetry-proto
    opentelemetry-propagator-jaeger
    opentelemetry-propagator-b3
    opentelemetry-exporter-zipkin-proto-http
    opentelemetry-exporter-zipkin-json
    opentelemetry-exporter-zipkin
    opentelemetry-exporter-otlp-proto-grpc
    opentelemetry-exporter-otlp
    opentelemetry-exporter-jaeger-thrift
    opentelemetry-exporter-jaeger-proto-grpc
    opentelemetry-exporter-jaeger
    opentelemetry-api

[prerelease]
version=0.45b0

packages=
    all
    opentelemetry-semantic-conventions
    opentelemetry-test-utils
    opentelemetry-instrumentation
    opentelemetry-contrib-instrumentations
    opentelemetry-distro
    opentelemetry-resource-detector-container

[exclude_release]
packages=
    opentelemetry-resource-detector-azure
    opentelemetry-sdk-extension-aws
    opentelemetry-propagator-aws-xray

[lintroots]
extraroots=examples/*,scripts/
subglob=*.py,tests/,test/,src/*,examples/*
ignore=sklearn

[testroots]
extraroots=examples/*,tests/
subglob=tests/,test/
