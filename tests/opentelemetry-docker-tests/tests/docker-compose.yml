version: '3'

services:
  otmongo:
    ports:
      - "27017:27017"
    image: mongo:latest
  otmysql:
    ports:
      - "3306:3306"
    image: mysql:latest
    restart: always
    environment:
      MYSQL_USER: testuser
      MYSQL_PASSWORD: testpassword
      MYSQL_ALLOW_EMPTY_PASSWORD: "yes"
      MYSQL_DATABASE: opentelemetry-tests
  otpostgres:
    image: postgres
    ports:
      - "5432:5432"
    environment:
      POSTGRES_USER: testuser
      POSTGRES_PASSWORD: testpassword
      POSTGRES_DB: opentelemetry-tests
  otredis:
      image: redis:4.0-alpine
      ports:
        - "127.0.0.1:6379:6379"
  otrediscluster:
      image: grokzen/redis-cluster:6.2.0
      environment:
          - IP=0.0.0.0
      ports:
          - "127.0.0.1:7000:7000"
          - "127.0.0.1:7001:7001"
          - "127.0.0.1:7002:7002"
          - "127.0.0.1:7003:7003"
          - "127.0.0.1:7004:7004"
          - "127.0.0.1:7005:7005"
  otjaeger:
    image: jaegertracing/all-in-one:1.8
    environment:
      COLLECTOR_ZIPKIN_HTTP_PORT: "9411"
    ports:
        - "5775:5775/udp"
        - "6831:6831/udp"
        - "6832:6832/udp"
        - "5778:5778"
        - "16686:16686"
        - "14268:14268"
        - "9411:9411"
  otmssql:
    image: mcr.microsoft.com/mssql/server:2017-CU23-ubuntu-16.04
    ports:
      - "1433:1433"
    environment:
      ACCEPT_EULA: "Y"
      SA_PASSWORD: "yourStrong(!)Password"
    command: /opt/mssql/bin/sqlservr
