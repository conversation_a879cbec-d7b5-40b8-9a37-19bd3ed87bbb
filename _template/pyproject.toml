[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
# opentelemetry-instrumentation plus the name of the library being instrument e.g
# name = "opentelemetry-instrumentation-sqlalchemy"
name = "<REPLACE ME>"
dynamic = ["version"]
# a description of the instrumentation e.g
# description = "SQLAlchemy tracing for OpenTelemetry"
description = "<REPLACE ME>"
readme = "README.rst"
license = "Apache-2.0"
requires-python = ">=3.8"
authors = [
  { name = "OpenTelemetry Authors", email = "<EMAIL>" },
]
classifiers = [
  "Development Status :: 4 - Beta",
  "Intended Audience :: Developers",
  "License :: OSI Approved :: Apache Software License",
  "Programming Language :: Python",
  "Programming Language :: Python :: 3",
  "Programming Language :: Python :: 3.8",
  "Programming Language :: Python :: 3.9",
  "Programming Language :: Python :: 3.10",
  "Programming Language :: Python :: 3.11",
]
dependencies = [
  "opentelemetry-api ~= 1.12",
]

[project.entry-points.opentelemetry_instrumentor]
# REPLACE ME: the entrypoint for the instrumentor e.g
# sqlalchemy = "opentelemetry.instrumentation.sqlalchemy:SQLAlchemyInstrumentor"
<REPLACE ME> = "opentelemetry.instrumentation.<REPLACE>"

[project.urls]
# url of the instrumentation e.g
# Homepage = "https://github.com/open-telemetry/opentelemetry-python-contrib/instrumentation/opentelemetry-instrumentation-sqlalchemy"
Homepage = "<REPLACE ME>"

[tool.hatch.version]
# REPLACE ME: the path to the version file e.g
# "src/opentelemetry/instrumentation/sqlalchemy/version.py"
path = "src/opentelemetry/instrumentation/<REPLACE ME>/version.py"

[tool.hatch.build.targets.sdist]
include = [
  "/src",
  "/tests",
]

[tool.hatch.build.targets.wheel]
packages = ["src/opentelemetry"]
