[default]
py-class=
    ; TODO: Understand why sphinx is not able to find this local class
    opentelemetry.propagators.textmap.CarrierT
    opentelemetry.propagators.textmap.Setter
    opentelemetry.propagators.textmap.Getter
    opentelemetry.propagators.textmap.TextMapPropagator
    ; - AwsXRayPropagator
    opentelemetry.propagators.textmap.DefaultGetter
    ; API
    opentelemetry.propagators.textmap.Getter
    ; - AWSXRayPropagator
    opentelemetry.sdk.trace.id_generator.IdGenerator
    opentelemetry.instrumentation.confluent_kafka.ProxiedProducer
    opentelemetry.instrumentation.confluent_kafka.ProxiedConsumer
    opentelemetry.instrumentation.instrumentor.BaseInstrumentor
    ; - AwsXRayIdGenerator
    TextMapPropagator
    CarrierT
    Setter
    Getter
    ; - AwsXRayPropagator.extract
    ; httpx changes __module__ causing Sphinx to error and no Sphinx site is available
    httpx.Client
    httpx.AsyncClient
    httpx.BaseTransport
    httpx.AsyncBaseTransport
    httpx.SyncByteStream
    httpx.AsyncByteStream
    httpx.Response
    yarl.URL
    cimpl.Producer
    cimpl.Consumer
    func
    Message
    TopicPartition
    callable
    Consumer
    confluent_kafka.Message

any=
    ; API
    opentelemetry.propagators.textmap.TextMapPropagator.fields
    ; - AWSXRayPropagator
    TraceId
    ; - AwsXRayIdGenerator
    TraceIdRatioBased
    ; - AwsXRayIdGenerator
    ; SDK
    SpanProcessor
    TracerProvider
    ; - AwsXRayIdGenerator
    ; Instrumentation
    BaseInstrumentor
    ; - instrumentation.*
    Setter
    httpx
    instrument
    __iter__
    list.__iter__
    __getitem__
    list.__getitem__
    SQS.ReceiveMessage

py-obj=
    opentelemetry.propagators.textmap.CarrierT
    
py-func=
    poll
    flush
    Message.error

py-exc=
    KafkaException
    KafkaError
