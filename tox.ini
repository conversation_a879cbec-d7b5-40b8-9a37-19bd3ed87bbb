[tox]
isolated_build = True
skipsdist = True
skip_missing_interpreters = True
envlist =
    ; Environments are organized by individual package, allowing
    ; for specifying supported Python versions per package.

    ; opentelemetry-resource-detector-container
    py3{8,9,10,11}-test-resource-detector-container
    pypy3-test-resource-detector-container

    ; opentelemetry-sdk-extension-aws
    py3{8,9,10,11}-test-sdk-extension-aws
    pypy3-test-sdk-extension-aws

    ; opentelemetry-distro
    py3{8,9,10,11}-test-distro
    pypy3-test-distro

    ; opentelemetry-instrumentation
    py3{8,9,10,11}-test-opentelemetry-instrumentation
    pypy3-test-opentelemetry-instrumentation

    ; opentelemetry-instrumentation-aiohttp-client
    py3{8,9,10,11}-test-instrumentation-aiohttp-client
    pypy3-test-instrumentation-aiohttp-client

    ; opentelemetry-instrumentation-aiohttp-server
    py3{6,8,9,10,11}-test-instrumentation-aiohttp-server
    pypy3-test-instrumentation-aiohttp-server

    ; opentelemetry-instrumentation-aiopg
    py3{8,9,10,11}-test-instrumentation-aiopg
    ; instrumentation-aiopg intentionally excluded from pypy3

    ; opentelemetry-instrumentation-aws-lambda
    py3{8,9,10,11}-test-instrumentation-aws-lambda
    pypy3-test-instrumentation-aws-lambda

    ; opentelemetry-instrumentation-botocore
    py3{8,9,10,11}-test-instrumentation-botocore
    ; FIXME: see https://github.com/open-telemetry/opentelemetry-python-contrib/issues/1736
    ; pypy3-test-instrumentation-botocore

    ; opentelemetry-instrumentation-boto3sqs
    py3{6,8,9,10,11}-test-instrumentation-boto3sqs
    ; FIXME: see https://github.com/open-telemetry/opentelemetry-python-contrib/issues/1736
    ; pypy3-test-instrumentation-boto3sqs

    ; opentelemetry-instrumentation-django
    ; Only officially supported Python versions are tested for each Django
    ; major release. Updated list can be found at:
    ; https://docs.djangoproject.com/en/dev/faq/install/#what-python-version-can-i-use-with-django
    ; The numbers at the end of the environment names
    ; below mean these dependencies are being used:
    ; 0: django~=2.0
    ; 1: django~=3.0
    ; 2: django>=4.0b1,<5.0 backports.zoneinfo==0.2.1
    ; 3: django>=4.0b1,<5.0
    py3{8,9}-test-instrumentation-django-0
    py3{8,9}-test-instrumentation-django-1
    py3{8,9}-test-instrumentation-django-2
    py3{10,11}-test-instrumentation-django-1
    py3{10,11}-test-instrumentation-django-3
    pypy3-test-instrumentation-django-{0,1}

    ; opentelemetry-instrumentation-dbapi
    py3{8,9,10,11}-test-instrumentation-dbapi
    pypy3-test-instrumentation-dbapi

    ; opentelemetry-instrumentation-boto
    py3{8,9,10,11}-test-instrumentation-boto
    ; FIXME: see https://github.com/open-telemetry/opentelemetry-python-contrib/issues/1736
    ; pypy3-test-instrumentation-boto

    ; opentelemetry-instrumentation-elasticsearch
    ; FIXME: Elasticsearch >=7 causes CI workflow tests to hang, see open-telemetry/opentelemetry-python-contrib#620
    ; The numbers at the end of the environment names
    ; below mean these dependencies are being used:
    ; 0: elasticsearch-dsl>=2.0,<3.0 elasticsearch>=2.0,<3.0
    ; 1: elasticsearch-dsl>=5.0,<6.0 elasticsearch>=5.0,<6.0
    ; 2: elasticsearch-dsl>=6.0,<7.0 elasticsearch>=6.0,<7.0
    py3{8,9,10,11}-test-instrumentation-elasticsearch-{0,2}
    pypy3-test-instrumentation-elasticsearch-{0,2}
    py3{8,9}-test-instrumentation-elasticsearch-1
    pypy3-test-instrumentation-elasticsearch-1

    ; opentelemetry-instrumentation-falcon
    ; py310 does not work with falcon 1
    ; The numbers at the end of the environment names
    ; below mean these dependencies are being used:
    ; 0: falcon ==1.4.1
    ; 1: falcon >=2.0.0,<3.0.0
    ; 2: falcon >=3.0.0,<4.0.0
    py3{8,9}-test-instrumentation-falcon-0
    py3{8,9,10,11}-test-instrumentation-falcon-{1,2}
    pypy3-test-instrumentation-falcon-{0,1,2}

    ; opentelemetry-instrumentation-fastapi
    py3{8,9,10,11}-test-instrumentation-fastapi
    pypy3-test-instrumentation-fastapi

    ; opentelemetry-instrumentation-flask
    ; The numbers at the end of the environment names
    ; below mean these dependencies are being used:
    ; 0: Flask ==2.1.3 Werkzeug <3.0.0
    ; 1: Flask ==2.2.0 Werkzeug <3.0.0
    ; 2: Flask >=3.0.0 Werkzeug >=3.0.0
    py3{8,9,10,11}-test-instrumentation-flask-{0,1}
    py3{8,9,10,11}-test-instrumentation-flask-{2}
    pypy3-test-instrumentation-flask-{0,1}

    ; opentelemetry-instrumentation-urllib
    py3{8,9,10,11}-test-instrumentation-urllib
    pypy3-test-instrumentation-urllib

    ; opentelemetry-instrumentation-urllib3
    ; The numbers at the end of the environment names
    ; below mean these dependencies are being used:
    ; 0: urllib3 >=1.0.0,<2.0.0
    ; 1: urllib3 >=2.0.0,<3.0.0
    py3{8,9,10,11}-test-instrumentation-urllib3-{0,1}
    pypy3-test-instrumentation-urllib3-{0,1}

    ; opentelemetry-instrumentation-requests
    py3{8,9,10,11}-test-instrumentation-requests
    ;pypy3-test-instrumentation-requests

    ; opentelemetry-instrumentation-starlette
    py3{8,9,10,11}-test-instrumentation-starlette
    pypy3-test-instrumentation-starlette

    ; opentelemetry-instrumentation-jinja2
    py3{8,9,10,11}-test-instrumentation-jinja2
    pypy3-test-instrumentation-jinja2

    ; opentelemetry-instrumentation-logging
    py3{8,9,10,11}-test-instrumentation-logging
    pypy3-test-instrumentation-logging

    ; opentelemetry-exporter-richconsole
    py3{8,9,10,11}-test-exporter-richconsole
    pypy3-test-exporter-richconsole

    ; opentelemetry-exporter-prometheus-remote-write
    py3{6,8,9,10,11}-test-exporter-prometheus-remote-write
    pypy3-test-exporter-prometheus-remote-write

    ; opentelemetry-instrumentation-mysql
    py3{8,9,10,11}-test-instrumentation-mysql
    pypy3-test-instrumentation-mysql

    ; opentelemetry-instrumentation-mysqlclient
    py3{8,9,10,11}-test-instrumentation-mysqlclient
    pypy3-test-instrumentation-mysqlclient

    ; opentelemetry-instrumentation-psycopg2
    py3{8,9,10,11}-test-instrumentation-psycopg2
    ; ext-psycopg2 intentionally excluded from pypy3

    ; opentelemetry-instrumentation-psycopg
    py3{7,8,9,10,11}-test-instrumentation-psycopg
    pypy3-test-instrumentation-psycopg

    ; opentelemetry-instrumentation-pymemcache
    ; The numbers at the end of the environment names
    ; below mean these dependencies are being used:
    ; 0: pymemcache ==1.3.5
    ; 1: pymemcache >2.0.0,<3.0.0
    ; 2: pymemcache >3.0.0,<3.4.2
    ; 3: pymemcache ==3.4.2
    ; 4: pymemcache ==4.0.0
    py3{8,9,10,11}-test-instrumentation-pymemcache-{0,1,2,3,4}
    pypy3-test-instrumentation-pymemcache-{0,1,2,3,4}

    ; opentelemetry-instrumentation-pymongo
    py3{8,9,10,11}-test-instrumentation-pymongo
    pypy3-test-instrumentation-pymongo

    ; opentelemetry-instrumentation-pymysql
    py3{8,9,10,11}-test-instrumentation-pymysql
    pypy3-test-instrumentation-pymysql

    ; opentelemetry-instrumentation-pyramid
    py3{8,9,10,11}-test-instrumentation-pyramid
    pypy3-test-instrumentation-pyramid

    ; opentelemetry-instrumentation-asgi
    py3{8,9,10,11}-test-instrumentation-asgi
    pypy3-test-instrumentation-asgi

    ; opentelemetry-instrumentation-asyncpg
    py3{8,9,10,11}-test-instrumentation-asyncpg
    ; ext-asyncpg intentionally excluded from pypy3

    ; opentelemetry-instrumentation-sqlite3
    py3{8,9,10,11}-test-instrumentation-sqlite3
    pypy3-test-instrumentation-sqlite3

    ; opentelemetry-instrumentation-wsgi
    py3{8,9,10,11}-test-instrumentation-wsgi
    pypy3-test-instrumentation-wsgi

    ; opentelemetry-instrumentation-grpc
    py3{8,9,10,11}-test-instrumentation-grpc
    pypy3-test-instrumentation-grpc

    ; opentelemetry-instrumentation-sqlalchemy
    ; The numbers at the end of the environment names
    ; below mean these dependencies are being used:
    ; 0: sqlalchemy>=1.1,<1.2
    ; 1: sqlalchemy~=1.4 aiosqlite
    py3{8,9,10,11}-test-instrumentation-sqlalchemy-{1}
    pypy3-test-instrumentation-sqlalchemy-{0,1}

    ; opentelemetry-instrumentation-redis
    py3{8,9,10,11}-test-instrumentation-redis
    pypy3-test-instrumentation-redis

    ; opentelemetry-instrumentation-remoulade
    py3{8,9,10,11}-test-instrumentation-remoulade
    ; instrumentation-remoulade intentionally excluded from pypy3

    ; opentelemetry-instrumentation-celery
    py3{8,9,10,11}-test-instrumentation-celery
    pypy3-test-instrumentation-celery

    ; opentelemetry-instrumentation-sklearn
    py3{8}-test-instrumentation-sklearn

    ; opentelemetry-instrumentation-system-metrics
    py3{6,8,9,10,11}-test-instrumentation-system-metrics
    pypy3-test-instrumentation-system-metrics

    ; opentelemetry-instrumentation-tornado
    py3{8,9,10,11}-test-instrumentation-tornado
    pypy3-test-instrumentation-tornado

    ; opentelemetry-instrumentation-tortoiseorm
    py3{8,9,10,11}-test-instrumentation-tortoiseorm
    pypy3-test-instrumentation-tortoiseorm

    ; opentelemetry-instrumentation-httpx
    py3{8,9,10,11}-test-instrumentation-httpx-{0,1}
    pypy3-test-instrumentation-httpx-{0,1}

    ; opentelemetry-util-http
    py3{8,9,10,11}-test-util-http
    pypy3-test-util-http

    ; opentelemetry-propagator-aws-xray
    py3{8,9,10,11}-test-propagator-aws-xray
    pypy3-test-propagator-aws-xray

    ; opentelemetry-propagator-ot-trace
    py3{8,9,10,11}-test-propagator-ot-trace
    pypy3-test-propagator-ot-trace

    ; opentelemetry-instrumentation-sio-pika
    ; The numbers at the end of the environment names
    ; below mean these dependencies are being used:
    ; 0: pika>=0.12.0,<1.0.0
    ; 1: pika>=1.0.0
    py3{8,9,10,11}-test-instrumentation-sio-pika-{0,1}
    pypy3-test-instrumentation-sio-pika-{0,1}

    ; opentelemetry-instrumentation-aio-pika
    ; The numbers at the end of the environment names
    ; below mean these dependencies are being used:
    ; 0: aio_pika~=7.2.0
    ; 1: aio_pika>=8.0.0,<9.0.0
    ; 2: aio_pika>=9.0.0,<10.0.0
    py3{8,9,10,11}-test-instrumentation-aio-pika-{0,1,2}
    pypy3-test-instrumentation-aio-pika-{0,1,2}

    ; opentelemetry-instrumentation-kafka-python
    py3{8,9,10,11}-test-instrumentation-kafka-python
    pypy3-test-instrumentation-kafka-python

    ; opentelemetry-instrumentation-confluent-kafka
    py3{8,9,10,11}-test-instrumentation-confluent-kafka
    pypy3-test-instrumentation-confluent-kafka

    ; opentelemetry-instrumentation-asyncio
    py3{8,9,10,11}-test-instrumentation-asyncio

    ; opentelemetry-instrumentation-cassandra
    py3{8,9,10,11}-test-instrumentation-cassandra
    pypy3-test-instrumentation-cassandra

    lint
    spellcheck
    docker-tests
    docs

    generate

[testenv]
deps =
  -c dev-requirements.txt
  test: pytest
  test: pytest-benchmark
  coverage: pytest
  coverage: pytest-cov
  grpc: pytest-asyncio

  ; FIXME: add coverage testing
  ; FIXME: add mypy testing

setenv =
  ; override CORE_REPO_SHA via env variable when testing other branches/commits than main
  ; i.e: CORE_REPO_SHA=dde62cebffe519c35875af6d06fae053b3be65ec tox -e <env to test>
  CORE_REPO_SHA={env:CORE_REPO_SHA:main}
  CORE_REPO=git+https://github.com/open-telemetry/opentelemetry-python.git@{env:CORE_REPO_SHA}

commands_pre =
; Install without -e to test the actual installation
  py3{8,9,10,11}: python -m pip install -U pip setuptools wheel
; Install common packages for all the tests. These are not needed in all the
; cases but it saves a lot of boilerplate in this file.
  test: pip install opentelemetry-api@{env:CORE_REPO}\#egg=opentelemetry-api&subdirectory=opentelemetry-api
  test: pip install opentelemetry-semantic-conventions@{env:CORE_REPO}\#egg=opentelemetry-semantic-conventions&subdirectory=opentelemetry-semantic-conventions
  test: pip install opentelemetry-sdk@{env:CORE_REPO}\#egg=opentelemetry-sdk&subdirectory=opentelemetry-sdk
  test: pip install opentelemetry-test-utils@{env:CORE_REPO}\#egg=opentelemetry-test-utils&subdirectory=tests/opentelemetry-test-utils
  test: pip install {toxinidir}/opentelemetry-instrumentation

  opentelemetry-instrumentation: pip install -r {toxinidir}/opentelemetry-instrumentation/test-requirements.txt

  distro: pip install -r {toxinidir}/opentelemetry-distro/test-requirements.txt

  asgi: pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-asgi/test-requirements.txt

  py3{8,9}-test-instrumentation-celery: pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-celery/test-requirements-0.txt
  py3{10,11}-test-instrumentation-celery: pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-celery/test-requirements-1.txt
  pypy3-test-instrumentation-celery: pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-celery/test-requirements-1.txt

  sio-pika-0: pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-pika/test-requirements-0.txt
  sio-pika-1: pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-pika/test-requirements-1.txt

  aio-pika-0: pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-aio-pika/test-requirements-0.txt
  aio-pika-1: pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-aio-pika/test-requirements-1.txt
  aio-pika-2: pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-aio-pika/test-requirements-2.txt

  kafka-python: pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-kafka-python/test-requirements.txt

  confluent-kafka: pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-confluent-kafka/test-requirements.txt

  grpc: pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-grpc/test-requirements.txt

  wsgi: pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-wsgi/test-requirements.txt

  asyncpg: pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-asyncpg/test-requirements.txt

  aws-lambda: pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-aws-lambda/test-requirements.txt

  boto: pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-boto/test-requirements.txt

  boto3sqs: pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-boto3sqs/test-requirements.txt

  falcon-0: pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-falcon/test-requirements-0.txt
  falcon-1: pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-falcon/test-requirements-1.txt
  falcon-2: pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-falcon/test-requirements-2.txt

  flask-0: pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-flask/test-requirements-0.txt
  flask-1: pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-flask/test-requirements-1.txt
  flask-2: pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-flask/test-requirements-2.txt

  urllib: pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-urllib/test-requirements.txt

  urllib3-0: pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-urllib3/test-requirements-0.txt
  urllib3-1: pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-urllib3/test-requirements-1.txt

  botocore: pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-botocore/test-requirements.txt

  cassandra: pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-cassandra/test-requirements.txt

  dbapi: pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-dbapi/test-requirements.txt

  py3{8,9}-test-instrumentation-django-0: pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-django/test-requirements-0.txt
  py3{8,9}-test-instrumentation-django-1: pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-django/test-requirements-1.txt
  py3{8,9}-test-instrumentation-django-2: pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-django/test-requirements-2.txt
  py3{10,11}-test-instrumentation-django-1: pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-django/test-requirements-1.txt
  py3{10,11}-test-instrumentation-django-3: pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-django/test-requirements-3.txt
  pypy3-test-instrumentation-django-0: pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-django/test-requirements-0.txt
  pypy3-test-instrumentation-django-1: pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-django/test-requirements-1.txt

  fastapi: pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-fastapi/test-requirements.txt

  mysql: pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-mysql/test-requirements.txt

  mysqlclient: pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-mysqlclient/test-requirements.txt

  pymemcache-0: pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-pymemcache/test-requirements-0.txt
  pymemcache-1: pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-pymemcache/test-requirements-1.txt
  pymemcache-2: pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-pymemcache/test-requirements-2.txt
  pymemcache-3: pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-pymemcache/test-requirements-3.txt
  pymemcache-4: pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-pymemcache/test-requirements-4.txt

  pymongo: pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-pymongo/test-requirements.txt

  py3{8,9}-test-instrumentation-psycopg: pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-psycopg/test-requirements-0.txt
  py3{10,11}-test-instrumentation-psycopg: pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-psycopg/test-requirements-1.txt
  pypy3-test-instrumentation-psycopg: pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-psycopg/test-requirements-1.txt

  psycopg2: pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-psycopg2/test-requirements.txt

  pymysql: pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-pymysql/test-requirements.txt

  pyramid: pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-pyramid/test-requirements.txt

  sqlite3: pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-sqlite3/test-requirements.txt

  redis: pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-redis/test-requirements.txt

  remoulade: pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-remoulade/test-requirements.txt

  requests: pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-requests/test-requirements.txt

  starlette: pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-starlette/test-requirements.txt

  system-metrics: pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-system-metrics/test-requirements.txt

  tornado: pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-tornado/test-requirements.txt

  tortoiseorm: pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-tortoiseorm/test-requirements.txt

  jinja2: pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-jinja2/test-requirements.txt

  logging: pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-logging/test-requirements.txt

  aio-pika-{0,1,2}: pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-aio-pika/test-requirements-2.txt

  aiohttp-client: pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-aiohttp-client/test-requirements.txt

  aiohttp-server: pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-aiohttp-server/test-requirements.txt

  aiopg: pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-aiopg/test-requirements.txt

  richconsole: pip install -r {toxinidir}/exporter/opentelemetry-exporter-richconsole/test-requirements.txt

  prometheus: pip install -r {toxinidir}/exporter/opentelemetry-exporter-prometheus-remote-write/test-requirements.txt

  sklearn: pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-sklearn/test-requirements.txt

  sqlalchemy-0: pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-sqlalchemy/test-requirements-0.txt
  sqlalchemy-1: pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-sqlalchemy/test-requirements-1.txt

  elasticsearch-0: pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-elasticsearch/test-requirements-0.txt
  elasticsearch-1: pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-elasticsearch/test-requirements-1.txt
  elasticsearch-2: pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-elasticsearch/test-requirements-2.txt

  asyncio: pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-asyncio/test-requirements.txt

  ; The numbers at the end of the environment names
  ; below mean these dependencies are being used:
  ; 0: httpx>=0.18.0,<0.19.0 respx~=0.17.0
  ; 1: httpx>=0.19.0 respx~=0.20.1
  httpx-0: pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-httpx/test-requirements-0.txt
  httpx-1: pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-httpx/test-requirements-1.txt

  sdk-extension-aws: pip install -r {toxinidir}/sdk-extension/opentelemetry-sdk-extension-aws/test-requirements.txt

  resource-detector-container: pip install -r {toxinidir}/resource/opentelemetry-resource-detector-container/test-requirements.txt

  http: pip install {toxinidir}/util/opentelemetry-util-http
; In order to get a health coverage report,
  propagator-ot-trace: pip install -r {toxinidir}/propagator/opentelemetry-propagator-ot-trace/test-requirements.txt

  propagator-aws-xray: pip install -r {toxinidir}/propagator/opentelemetry-propagator-aws-xray/test-requirements.txt

; we have to install packages in editable mode.
  coverage: python {toxinidir}/scripts/eachdist.py install --editable

commands =
  test-distro: pytest {toxinidir}/opentelemetry-distro/tests {posargs}
  test-opentelemetry-instrumentation: pytest {toxinidir}/opentelemetry-instrumentation/tests {posargs}
  test-instrumentation-aiohttp-client: pytest {toxinidir}/instrumentation/opentelemetry-instrumentation-aiohttp-client/tests {posargs}
  test-instrumentation-aiohttp-server: pytest {toxinidir}/instrumentation/opentelemetry-instrumentation-aiohttp-server/tests {posargs}
  test-instrumentation-aiopg: pytest {toxinidir}/instrumentation/opentelemetry-instrumentation-aiopg/tests {posargs}
  test-instrumentation-asgi: pytest {toxinidir}/instrumentation/opentelemetry-instrumentation-asgi/tests {posargs}
  test-instrumentation-asyncpg: pytest {toxinidir}/instrumentation/opentelemetry-instrumentation-asyncpg/tests {posargs}
  test-instrumentation-aws-lambda: pytest {toxinidir}/instrumentation/opentelemetry-instrumentation-aws-lambda/tests {posargs}
  test-instrumentation-boto: pytest {toxinidir}/instrumentation/opentelemetry-instrumentation-boto/tests {posargs}
  test-instrumentation-botocore: pytest {toxinidir}/instrumentation/opentelemetry-instrumentation-botocore/tests {posargs}
  test-instrumentation-boto3sqs: pytest {toxinidir}/instrumentation/opentelemetry-instrumentation-boto3sqs/tests {posargs}
  test-instrumentation-cassandra: pytest {toxinidir}/instrumentation/opentelemetry-instrumentation-cassandra/tests {posargs}
  test-instrumentation-celery: pytest {toxinidir}/instrumentation/opentelemetry-instrumentation-celery/tests {posargs}
  test-instrumentation-dbapi: pytest {toxinidir}/instrumentation/opentelemetry-instrumentation-dbapi/tests {posargs}
  test-instrumentation-django: pytest {toxinidir}/instrumentation/opentelemetry-instrumentation-django/tests {posargs}
  test-instrumentation-elasticsearch: pytest {toxinidir}/instrumentation/opentelemetry-instrumentation-elasticsearch/tests {posargs}
  test-instrumentation-falcon: pytest {toxinidir}/instrumentation/opentelemetry-instrumentation-falcon/tests {posargs}
  test-instrumentation-fastapi: pytest {toxinidir}/instrumentation/opentelemetry-instrumentation-fastapi/tests {posargs}
  test-instrumentation-flask: pytest {toxinidir}/instrumentation/opentelemetry-instrumentation-flask/tests {posargs}
  test-instrumentation-urllib: pytest {toxinidir}/instrumentation/opentelemetry-instrumentation-urllib/tests {posargs}
  test-instrumentation-urllib3: pytest {toxinidir}/instrumentation/opentelemetry-instrumentation-urllib3/tests {posargs}
  test-instrumentation-grpc: pytest {toxinidir}/instrumentation/opentelemetry-instrumentation-grpc/tests {posargs}
  test-instrumentation-jinja2: pytest {toxinidir}/instrumentation/opentelemetry-instrumentation-jinja2/tests {posargs}
  test-instrumentation-kafka-python: pytest {toxinidir}/instrumentation/opentelemetry-instrumentation-kafka-python/tests {posargs}
  test-instrumentation-confluent-kafka: pytest {toxinidir}/instrumentation/opentelemetry-instrumentation-confluent-kafka/tests {posargs}
  test-instrumentation-logging: pytest {toxinidir}/instrumentation/opentelemetry-instrumentation-logging/tests {posargs}
  test-instrumentation-mysql: pytest {toxinidir}/instrumentation/opentelemetry-instrumentation-mysql/tests {posargs}
  test-instrumentation-mysqlclient: pytest {toxinidir}/instrumentation/opentelemetry-instrumentation-mysqlclient/tests {posargs}
  test-instrumentation-sio-pika: pytest {toxinidir}/instrumentation/opentelemetry-instrumentation-pika/tests {posargs}
  test-instrumentation-aio-pika: pytest {toxinidir}/instrumentation/opentelemetry-instrumentation-aio-pika/tests {posargs}
  test-instrumentation-psycopg: pytest {toxinidir}/instrumentation/opentelemetry-instrumentation-psycopg/tests {posargs}
  test-instrumentation-psycopg2: pytest {toxinidir}/instrumentation/opentelemetry-instrumentation-psycopg2/tests {posargs}
  test-instrumentation-pymemcache: pytest {toxinidir}/instrumentation/opentelemetry-instrumentation-pymemcache/tests {posargs}
  test-instrumentation-pymongo: pytest {toxinidir}/instrumentation/opentelemetry-instrumentation-pymongo/tests {posargs}
  test-instrumentation-pymysql: pytest {toxinidir}/instrumentation/opentelemetry-instrumentation-pymysql/tests {posargs}
  test-instrumentation-pyramid: pytest {toxinidir}/instrumentation/opentelemetry-instrumentation-pyramid/tests {posargs}
  test-instrumentation-redis: pytest {toxinidir}/instrumentation/opentelemetry-instrumentation-redis/tests {posargs}
  test-instrumentation-remoulade: pytest {toxinidir}/instrumentation/opentelemetry-instrumentation-remoulade/tests {posargs}
  test-instrumentation-requests: pytest {toxinidir}/instrumentation/opentelemetry-instrumentation-requests/tests {posargs}
  test-instrumentation-sklearn: pytest {toxinidir}/instrumentation/opentelemetry-instrumentation-sklearn/tests {posargs}
  test-instrumentation-sqlalchemy: pytest {toxinidir}/instrumentation/opentelemetry-instrumentation-sqlalchemy/tests {posargs}
  test-instrumentation-sqlite3: pytest {toxinidir}/instrumentation/opentelemetry-instrumentation-sqlite3/tests {posargs}
  test-instrumentation-starlette: pytest {toxinidir}/instrumentation/opentelemetry-instrumentation-starlette/tests {posargs}
  test-instrumentation-system-metrics: pytest {toxinidir}/instrumentation/opentelemetry-instrumentation-system-metrics/tests {posargs}
  test-instrumentation-tornado: pytest {toxinidir}/instrumentation/opentelemetry-instrumentation-tornado/tests {posargs}
  test-instrumentation-tortoiseorm: pytest {toxinidir}/instrumentation/opentelemetry-instrumentation-tortoiseorm/tests {posargs}
  test-instrumentation-wsgi: pytest {toxinidir}/instrumentation/opentelemetry-instrumentation-wsgi/tests {posargs}
  test-instrumentation-httpx: pytest {toxinidir}/instrumentation/opentelemetry-instrumentation-httpx/tests {posargs}
  test-instrumentation-asyncio: pytest {toxinidir}/instrumentation/opentelemetry-instrumentation-asyncio/tests {posargs}
  test-util-http: pytest {toxinidir}/util/opentelemetry-util-http/tests {posargs}
  test-sdk-extension-aws: pytest {toxinidir}/sdk-extension/opentelemetry-sdk-extension-aws/tests {posargs}
  test-resource-detector-container: pytest {toxinidir}/resource/opentelemetry-resource-detector-container/tests {posargs}
  test-propagator-aws: pytest {toxinidir}/propagator/opentelemetry-propagator-aws-xray/tests {posargs}
  test-propagator-ot-trace: pytest {toxinidir}/propagator/opentelemetry-propagator-ot-trace/tests {posargs}
  test-exporter-richconsole: pytest {toxinidir}/exporter/opentelemetry-exporter-richconsole/tests {posargs}
  test-exporter-prometheus-remote-write: pytest {toxinidir}/exporter/opentelemetry-exporter-prometheus-remote-write/tests {posargs}
  coverage: {toxinidir}/scripts/coverage.sh

[testenv:docs]
deps =
  -c {toxinidir}/dev-requirements.txt
  -r {toxinidir}/docs-requirements.txt
  pytest

commands_pre =
  python -m pip install {env:CORE_REPO}\#egg=opentelemetry-api&subdirectory=opentelemetry-api
  python -m pip install {env:CORE_REPO}\#egg=opentelemetry-semantic-conventions&subdirectory=opentelemetry-semantic-conventions
  python -m pip install {env:CORE_REPO}\#egg=opentelemetry-sdk&subdirectory=opentelemetry-sdk
  python -m pip install {toxinidir}/opentelemetry-instrumentation
  python -m pip install {toxinidir}/util/opentelemetry-util-http

changedir = docs

commands =
  sphinx-build -E -a -W -b html -T . _build/html

[testenv:spellcheck]
basepython: python3
recreate = True
deps =
  codespell

commands =
  codespell

[testenv:lint]
basepython: python3
recreate = True
deps =
  -r dev-requirements.txt

commands_pre =
  python -m pip install {env:CORE_REPO}\#egg=opentelemetry-api&subdirectory=opentelemetry-api
  python -m pip install {env:CORE_REPO}\#egg=opentelemetry-semantic-conventions&subdirectory=opentelemetry-semantic-conventions
  python -m pip install {env:CORE_REPO}\#egg=opentelemetry-sdk&subdirectory=opentelemetry-sdk
  python -m pip install {env:CORE_REPO}\#egg=opentelemetry-test-utils&subdirectory=tests/opentelemetry-test-utils
  python -m pip install -e {toxinidir}/util/opentelemetry-util-http[test]
  pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-wsgi/test-requirements.txt
  pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-asgi/test-requirements.txt
  pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-dbapi/test-requirements.txt
  pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-botocore/test-requirements.txt
  pip install -r {toxinidir}/opentelemetry-instrumentation/test-requirements.txt
  pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-django/test-requirements-3.txt
  pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-starlette/test-requirements.txt
  pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-falcon/test-requirements-2.txt
  pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-grpc/test-requirements.txt
  pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-flask/test-requirements-2.txt
  pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-sqlalchemy/test-requirements-1.txt
  pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-cassandra/test-requirements.txt
  pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-celery/test-requirements-1.txt
  pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-boto/test-requirements.txt
  pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-boto3sqs/test-requirements.txt
  pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-aio-pika/test-requirements-2.txt
  pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-redis/test-requirements.txt
  pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-fastapi/test-requirements.txt
  pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-jinja2/test-requirements.txt
  pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-kafka-python/test-requirements.txt
  pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-remoulade/test-requirements.txt
  ; pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-sklearn/test-requirements.txt
  pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-pymemcache/test-requirements-4.txt
  pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-logging/test-requirements.txt
  pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-aiohttp-client/test-requirements.txt
  pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-aiopg/test-requirements.txt
  pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-aiohttp-server/test-requirements.txt
  pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-psycopg2/test-requirements.txt
  pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-psycopg/test-requirements-1.txt
  pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-pyramid/test-requirements.txt
  pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-requests/test-requirements.txt
  pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-urllib/test-requirements.txt
  pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-urllib3/test-requirements-1.txt
  pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-sqlite3/test-requirements.txt
  pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-confluent-kafka/test-requirements.txt
  pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-pika/test-requirements-1.txt
  pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-pymysql/test-requirements.txt
  # prerequisite: follow the instructions here https://github.com/PyMySQL/mysqlclient#install
  # for your OS to install the required dependencies
  pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-mysqlclient/test-requirements.txt
  pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-elasticsearch/test-requirements-2.txt
  pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-tornado/test-requirements.txt
  pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-asyncpg/test-requirements.txt
  pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-pymongo/test-requirements.txt
  pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-httpx/test-requirements-1.txt
  pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-aws-lambda/test-requirements.txt
  pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-mysql/test-requirements.txt
  pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-asyncio/test-requirements.txt
  pip install -r {toxinidir}/exporter/opentelemetry-exporter-richconsole/test-requirements.txt
  pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-system-metrics/test-requirements.txt
  pip install -r {toxinidir}/instrumentation/opentelemetry-instrumentation-tortoiseorm/test-requirements.txt
  # requires snappy headers to be available on the system
  pip install -r {toxinidir}/resource/opentelemetry-resource-detector-container/test-requirements.txt
  pip install -r {toxinidir}/propagator/opentelemetry-propagator-aws-xray/test-requirements.txt
  pip install -r {toxinidir}/propagator/opentelemetry-propagator-ot-trace/test-requirements.txt
  pip install -r {toxinidir}/sdk-extension/opentelemetry-sdk-extension-aws/test-requirements.txt
  pip install -r {toxinidir}/opentelemetry-distro/test-requirements.txt

commands =
  python scripts/eachdist.py lint --check-only

[testenv:docker-tests]
basepython: python3
deps =
  aiopg==1.4.0
  amqp==5.2.0
  asgiref==3.7.2
  async-timeout==4.0.3
  asyncpg==0.29.0
  attrs==23.2.0
  bcrypt==4.1.2
  billiard==4.2.0
  celery==5.3.6
  certifi==2024.2.2
  cffi==1.16.0
  chardet==3.0.4
  click==8.1.7
  click-didyoumean==0.3.0
  click-plugins==1.1.1
  click-repl==0.3.0
  cryptography==42.0.5
  Deprecated==1.2.14
  distro==1.9.0
  dnspython==2.6.1
  docker==5.0.3
  docker-compose==1.29.2
  dockerpty==0.4.1
  docopt==0.6.2
  exceptiongroup==1.2.0
  flaky==3.7.0
  greenlet==3.0.3
  grpcio==1.62.1
  idna==2.10
  importlib-metadata==6.11.0
  iniconfig==2.0.0
  jsonschema==3.2.0
  kombu==5.3.5
  mysql-connector-python==8.3.0
  mysqlclient==2.1.1
  opencensus-proto==0.1.0
  packaging==24.0
  paramiko==3.4.0
  pluggy==1.4.0
  prometheus_client==0.20.0
  prompt-toolkit==3.0.43
  protobuf==3.20.3
  # prerequisite: install libpq-dev (debian) or postgresql-devel (rhel), postgresql (mac)
  # see https://www.psycopg.org/docs/install.html#build-prerequisites
  # you might have to install additional packages depending on your OS
  psycopg==3.1.18
  psycopg2==2.9.9
  psycopg2-binary==2.9.9
  pycparser==2.21
  pymongo==4.6.2
  PyMySQL==0.10.1
  PyNaCl==1.5.0
  # prerequisite: install unixodbc
  pyodbc==4.0.39
  pyrsistent==0.20.0
  pytest==8.0.2
  pytest-celery==0.0.0
  python-dateutil==2.9.0.post0
  python-dotenv==0.21.1
  pytz==2024.1
  PyYAML==5.3.1
  redis==5.0.1
  remoulade==3.2.0
  requests==2.25.0
  six==1.16.0
  SQLAlchemy==1.4.52
  texttable==1.7.0
  tomli==2.0.1
  typing_extensions==4.10.0
  tzdata==2024.1
  urllib3==1.26.18
  vine==5.1.0
  wcwidth==0.2.13
  websocket-client==0.59.0
  wrapt==1.16.0
  zipp==3.18.0

changedir =
  tests/opentelemetry-docker-tests/tests

commands_pre =
  pip install {env:CORE_REPO}\#egg=opentelemetry-api&subdirectory=opentelemetry-api \
              {env:CORE_REPO}\#egg=opentelemetry-semantic-conventions&subdirectory=opentelemetry-semantic-conventions \
              {env:CORE_REPO}\#egg=opentelemetry-sdk&subdirectory=opentelemetry-sdk \
              {env:CORE_REPO}\#egg=opentelemetry-test-utils&subdirectory=tests/opentelemetry-test-utils \
              -e {toxinidir}/opentelemetry-instrumentation \
              -e {toxinidir}/instrumentation/opentelemetry-instrumentation-asyncpg \
              -e {toxinidir}/instrumentation/opentelemetry-instrumentation-celery \
              -e {toxinidir}/instrumentation/opentelemetry-instrumentation-pika \
              -e {toxinidir}/instrumentation/opentelemetry-instrumentation-kafka-python \
              -e {toxinidir}/instrumentation/opentelemetry-instrumentation-confluent-kafka \
              -e {toxinidir}/instrumentation/opentelemetry-instrumentation-dbapi \
              -e {toxinidir}/instrumentation/opentelemetry-instrumentation-mysql \
              -e {toxinidir}/instrumentation/opentelemetry-instrumentation-mysqlclient \
              -e {toxinidir}/instrumentation/opentelemetry-instrumentation-psycopg \
              -e {toxinidir}/instrumentation/opentelemetry-instrumentation-psycopg2 \
              -e {toxinidir}/instrumentation/opentelemetry-instrumentation-pymongo \
              -e {toxinidir}/instrumentation/opentelemetry-instrumentation-pymysql \
              -e {toxinidir}/instrumentation/opentelemetry-instrumentation-sqlalchemy \
              -e {toxinidir}/instrumentation/opentelemetry-instrumentation-aiopg \
              -e {toxinidir}/instrumentation/opentelemetry-instrumentation-redis \
              -e {toxinidir}/instrumentation/opentelemetry-instrumentation-remoulade \
              {env:CORE_REPO}\#egg=opentelemetry-exporter-opencensus&subdirectory=exporter/opentelemetry-exporter-opencensus
  docker-compose up -d
  python check_availability.py

commands =
  pytest {posargs}

commands_post =
  docker-compose down -v

[testenv:generate]
deps =
  -r {toxinidir}/gen-requirements.txt

allowlist_externals =
  {toxinidir}/scripts/generate_instrumentation_bootstrap.py
  {toxinidir}/scripts/generate_instrumentation_readme.py
  {toxinidir}/scripts/generate_instrumentation_metapackage.py

commands =
  {toxinidir}/scripts/generate_instrumentation_bootstrap.py
  {toxinidir}/scripts/generate_instrumentation_readme.py
  {toxinidir}/scripts/generate_instrumentation_metapackage.py
