[build-system]
requires = [
    "hatchling",
]
build-backend = "hatchling.build"

[project]
name = "opentelemetry-contrib-instrumentations"
dynamic = [
    "version",
]
description = "OpenTelemetry Contrib Instrumentation Packages"
readme = "README.rst"
license = "Apache-2.0"
requires-python = ">=3.8"
authors = [
    { name = "OpenTelemetry Authors", email = "<EMAIL>" },
]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: Apache Software License",
    "Programming Language :: Python",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
]
dependencies = [
    "opentelemetry-instrumentation-aio-pika==0.45b0",
    "opentelemetry-instrumentation-aiohttp-client==0.45b0",
    "opentelemetry-instrumentation-aiohttp-server==0.45b0",
    "opentelemetry-instrumentation-aiopg==0.45b0",
    "opentelemetry-instrumentation-asgi==0.45b0",
    "opentelemetry-instrumentation-asyncio==0.45b0",
    "opentelemetry-instrumentation-asyncpg==0.45b0",
    "opentelemetry-instrumentation-aws-lambda==0.45b0",
    "opentelemetry-instrumentation-boto==0.45b0",
    "opentelemetry-instrumentation-boto3sqs==0.45b0",
    "opentelemetry-instrumentation-botocore==0.45b0",
    "opentelemetry-instrumentation-cassandra==0.45b0",
    "opentelemetry-instrumentation-celery==0.45b0",
    "opentelemetry-instrumentation-confluent-kafka==0.45b0",
    "opentelemetry-instrumentation-dbapi==0.45b0",
    "opentelemetry-instrumentation-django==0.45b0",
    "opentelemetry-instrumentation-elasticsearch==0.45b0",
    "opentelemetry-instrumentation-falcon==0.45b0",
    "opentelemetry-instrumentation-fastapi==0.45b0",
    "opentelemetry-instrumentation-flask==0.45b0",
    "opentelemetry-instrumentation-grpc==0.45b0",
    "opentelemetry-instrumentation-httpx==0.45b0",
    "opentelemetry-instrumentation-jinja2==0.45b0",
    "opentelemetry-instrumentation-kafka-python==0.45b0",
    "opentelemetry-instrumentation-logging==0.45b0",
    "opentelemetry-instrumentation-mysql==0.45b0",
    "opentelemetry-instrumentation-mysqlclient==0.45b0",
    "opentelemetry-instrumentation-pika==0.45b0",
    "opentelemetry-instrumentation-psycopg==0.45b0",
    "opentelemetry-instrumentation-psycopg2==0.45b0",
    "opentelemetry-instrumentation-pymemcache==0.45b0",
    "opentelemetry-instrumentation-pymongo==0.45b0",
    "opentelemetry-instrumentation-pymysql==0.45b0",
    "opentelemetry-instrumentation-pyramid==0.45b0",
    "opentelemetry-instrumentation-redis==0.45b0",
    "opentelemetry-instrumentation-remoulade==0.45b0",
    "opentelemetry-instrumentation-requests==0.45b0",
    "opentelemetry-instrumentation-sklearn==0.45b0",
    "opentelemetry-instrumentation-sqlalchemy==0.45b0",
    "opentelemetry-instrumentation-sqlite3==0.45b0",
    "opentelemetry-instrumentation-starlette==0.45b0",
    "opentelemetry-instrumentation-system-metrics==0.45b0",
    "opentelemetry-instrumentation-tornado==0.45b0",
    "opentelemetry-instrumentation-tortoiseorm==0.45b0",
    "opentelemetry-instrumentation-urllib==0.45b0",
    "opentelemetry-instrumentation-urllib3==0.45b0",
    "opentelemetry-instrumentation-wsgi==0.45b0",
]

[project.urls]
Homepage = "https://github.com/open-telemetry/opentelemetry-python-contrib/tree/main/opentelemetry-contrib-instrumentations"

[tool.hatch.version]
path = "src/opentelemetry/contrib-instrumentations/version.py"

[tool.hatch.build.targets.sdist]
include = [
    "/src",
]

[tool.hatch.build.targets.wheel]
packages = [
    "src/opentelemetry",
]
