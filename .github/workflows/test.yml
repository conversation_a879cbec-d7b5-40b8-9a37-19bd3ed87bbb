name: Contrib <PERSON><PERSON> Tests

on:
  push:
    branches-ignore:
    - 'release/*'
  pull_request:
env:
  CORE_REPO_SHA: 038ec878b045d97da39e8ff51d0420bbc3961042

jobs:
  misc:
    strategy:
      fail-fast: false
      matrix:
        tox-environment: [ "docker-tests", "spellcheck", "lint", "docs", "generate" ]
    name: ${{ matrix.tox-environment }}
    runs-on: ubuntu-20.04
    steps:
      - name: Checkout Contrib Repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v2
      - name: Set up Python 3.10
        uses: actions/setup-python@v2
        with:
          python-version: "3.10"
      - name: Install tox
        run: pip install tox
      - name: Install libsnappy-dev
        if: ${{ matrix.tox-environment == 'lint' }}
        run: sudo apt-get install -y libsnappy-dev
      - name: Cache tox environment
        # Preserves .tox directory between runs for faster installs
        uses: actions/cache@v1
        with:
          path: |
            .tox
            ~/.cache/pip
          key: v7-misc-tox-cache-${{ matrix.tox-environment }}-${{ hashFiles('tox.ini', 'dev-requirements.txt', 'gen-requirements.txt', 'docs-requirements.txt') }}
      - name: run tox
        run: tox -e ${{ matrix.tox-environment }}
      - name: Ensure generated code is up to date
        if: matrix.tox-environment == 'generate'
        run: git diff --exit-code || (echo 'Generated code is out of date, please run "tox -e generate" and commit the changes in this PR.' && exit 1)
