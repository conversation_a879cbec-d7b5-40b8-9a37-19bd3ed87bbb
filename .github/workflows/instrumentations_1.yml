name: Contrib Repo Tests

on:
  push:
    branches-ignore:
    - 'release/*'
  pull_request:
env:
  CORE_REPO_SHA: 038ec878b045d97da39e8ff51d0420bbc3961042

jobs:
  instrumentations-1:
    env:
      # We use these variables to convert between tox and GHA version literals
      py38: 3.8
      py39: 3.9
      py310: "3.10"
      py311: "3.11"
      pypy3: pypy-3.8
      RUN_MATRIX_COMBINATION: ${{ matrix.python-version }}-${{ matrix.package }}-${{ matrix.os }}
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false  # ensures the entire test matrix is run, even if one permutation fails
      matrix:
        python-version: [py38, py39, py310, py311, pypy3]
        package:
          - "urllib"
          - "urllib3"
          - "wsgi"
          - "distro"
          - "richconsole"
          - "psycopg"
          - "prometheus-remote-write"
          - "sdk-extension-aws"
          - "propagator-aws-xray"
          - "propagator-ot-trace"
          - "resource-detector-container"
        os: [ubuntu-20.04]
        exclude:
          - python-version: py311
            package: "prometheus-remote-write"
          - python-version: pypy3
            package: "prometheus-remote-write"
    steps:
      - name: Checkout Contrib Repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v2
      - name: Set up Python ${{ env[matrix.python-version] }}
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env[matrix.python-version] }}
      - name: Install tox
        run: pip install tox
      - name: Cache tox environment
        # Preserves .tox directory between runs for faster installs
        uses: actions/cache@v1
        with:
          path: |
            .tox
            ~/.cache/pip
          key: v7-build-tox-cache-${{ env.RUN_MATRIX_COMBINATION }}-${{ hashFiles('tox.ini', 'gen-requirements.txt', 'dev-requirements.txt') }}
      - name: run tox
        run: tox -f ${{ matrix.python-version }}-${{ matrix.package }} -- -ra --benchmark-json=${{ env.RUN_MATRIX_COMBINATION }}-benchmark.json
