name: Contrib <PERSON>o Tests

on:
  push:
    branches-ignore:
    - 'release/*'
  pull_request:
env:
  CORE_REPO_SHA: 038ec878b045d97da39e8ff51d0420bbc3961042

jobs:
  instrumentations-0:
    env:
      # We use these variables to convert between tox and GHA version literals
      py38: 3.8
      py39: 3.9
      py310: "3.10"
      py311: "3.11"
      pypy3: pypy-3.8
      RUN_MATRIX_COMBINATION: ${{ matrix.python-version }}-${{ matrix.package }}-${{ matrix.os }}
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false  # ensures the entire test matrix is run, even if one permutation fails
      matrix:
        python-version: [py38, py39, py310, py311, pypy3]
        package:
          # Do not add more instrumentations here, add them in instrumentations_1.yml.
          # The reason for this separation of instrumentations into more than one YAML file is
          # the limit of jobs that can be run from a Github actions matrix:
          # https://docs.github.com/en/actions/using-jobs/using-a-matrix-for-your-jobs
          # "A matrix will generate a maximum of 256 jobs per workflow run. This limit applies
          # to both GitHub-hosted and self-hosted runners."
          - "aiohttp-client"
          - "aiohttp-server"
          - "aiopg"
          - "aio-pika"
          - "asgi"
          - "asyncpg"
          - "aws-lambda"
          - "boto"
          - "boto3sqs"
          - "botocore"
          - "cassandra"
          - "celery"
          - "confluent-kafka"
          - "dbapi"
          - "django"
          - "elasticsearch"
          - "falcon"
          - "fastapi"
          - "flask"
          - "grpc"
          - "httpx"
          - "jinja2"
          - "kafka-python"
          - "logging"
          - "mysql"
          - "mysqlclient"
          - "sio-pika"
          - "psycopg2"
          - "pymemcache"
          - "pymongo"
          - "pymysql"
          - "pyramid"
          - "redis"
          - "remoulade"
          - "requests"
          - "sklearn"
          - "sqlalchemy"
          - "sqlite3"
          - "starlette"
          - "system-metrics"
          - "tornado"
          - "tortoiseorm"
        os: [ubuntu-20.04]
        exclude:
          - python-version: py39
            package: "sklearn"
          - python-version: py310
            package: "sklearn"
          - python-version: py311
            package: "sklearn"
          - python-version: pypy3
            package: "aiopg"
          - python-version: pypy3
            package: "asyncpg"
          - python-version: pypy3
            package: "boto"
          - python-version: pypy3
            package: "boto3sqs"
          - python-version: pypy3
            package: "botocore"
          - python-version: pypy3
            package: "psycopg2"
          - python-version: pypy3
            package: "remoulade"
          - python-version: pypy3
            package: "requests"
          - python-version: pypy3
            package: "sklearn"
          - python-version: pypy3
            package: "confluent-kafka"
          - python-version: pypy3
            package: "grpc"
    steps:
      - name: Checkout Contrib Repo @ SHA - ${{ github.sha }}
        uses: actions/checkout@v2
      - name: Set up Python ${{ env[matrix.python-version] }}
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env[matrix.python-version] }}
      - name: Install tox
        run: pip install tox
      - name: Cache tox environment
        # Preserves .tox directory between runs for faster installs
        uses: actions/cache@v1
        with:
          path: |
            .tox
            ~/.cache/pip
          key: v7-build-tox-cache-${{ env.RUN_MATRIX_COMBINATION }}-${{ hashFiles('tox.ini', 'gen-requirements.txt', 'dev-requirements.txt') }}
      - name: run tox
        run: tox -f ${{ matrix.python-version }}-${{ matrix.package }} -- -ra --benchmark-json=${{ env.RUN_MATRIX_COMBINATION }}-benchmark.json
