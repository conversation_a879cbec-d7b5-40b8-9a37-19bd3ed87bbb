[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "opentelemetry-instrumentation-grpc"
dynamic = ["version"]
description = "OpenTelemetry gRPC instrumentation"
readme = "README.rst"
license = "Apache-2.0"
requires-python = ">=3.8"
authors = [
  { name = "OpenTelemetry Authors", email = "<EMAIL>" },
]
classifiers = [
  "Development Status :: 4 - Beta",
  "Intended Audience :: Developers",
  "License :: OSI Approved :: Apache Software License",
  "Programming Language :: Python",
  "Programming Language :: Python :: 3",
  "Programming Language :: Python :: 3.8",
  "Programming Language :: Python :: 3.9",
  "Programming Language :: Python :: 3.10",
  "Programming Language :: Python :: 3.11",
]
dependencies = [
  "opentelemetry-api ~= 1.12",
  "opentelemetry-instrumentation == 0.45b0",
  "opentelemetry-sdk ~= 1.12",
  "opentelemetry-semantic-conventions == 0.45b0",
  "wrapt >= 1.0.0, < 2.0.0",
]

[project.optional-dependencies]
instruments = [
  "grpcio ~= 1.27",
]

[project.entry-points.opentelemetry_instrumentor]
grpc_client = "opentelemetry.instrumentation.grpc:GrpcInstrumentorClient"
grpc_server = "opentelemetry.instrumentation.grpc:GrpcInstrumentorServer"
grpc_aio_client = "opentelemetry.instrumentation.grpc:GrpcAioInstrumentorClient"
grpc_aio_server = "opentelemetry.instrumentation.grpc:GrpcAioInstrumentorServer"

[project.urls]
Homepage = "https://github.com/open-telemetry/opentelemetry-python-contrib/tree/main/instrumentation/opentelemetry-instrumentation-grpc"

[tool.hatch.version]
path = "src/opentelemetry/instrumentation/grpc/version.py"

[tool.hatch.build.targets.sdist]
include = [
  "/src",
  "/tests",
]

[tool.hatch.build.targets.wheel]
packages = ["src/opentelemetry"]
