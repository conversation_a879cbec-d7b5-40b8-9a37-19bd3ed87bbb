# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: test_server.proto

from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


DESCRIPTOR = _descriptor.FileDescriptor(
    name="test_server.proto",
    package="",
    syntax="proto3",
    serialized_options=None,
    serialized_pb=b'\n\x11test_server.proto"2\n\x07Request\x12\x11\n\tclient_id\x18\x01 \x01(\x03\x12\x14\n\x0crequest_data\x18\x02 \x01(\t"4\n\x08Response\x12\x11\n\tserver_id\x18\x01 \x01(\x03\x12\x15\n\rresponse_data\x18\x02 \x01(\t2\xce\x01\n\x0eGRPCTestServer\x12#\n\x0cSimpleMethod\x12\x08.Request\x1a\t.Response\x12.\n\x15\x43lientStreamingMethod\x12\x08.Request\x1a\t.Response(\x01\x12.\n\x15ServerStreamingMethod\x12\x08.Request\x1a\t.Response0\x01\x12\x37\n\x1c\x42idirectionalStreamingMethod\x12\x08.Request\x1a\t.Response(\x01\x30\x01\x62\x06proto3',
)


_REQUEST = _descriptor.Descriptor(
    name="Request",
    full_name="Request",
    filename=None,
    file=DESCRIPTOR,
    containing_type=None,
    fields=[
        _descriptor.FieldDescriptor(
            name="client_id",
            full_name="Request.client_id",
            index=0,
            number=1,
            type=3,
            cpp_type=2,
            label=1,
            has_default_value=False,
            default_value=0,
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
        _descriptor.FieldDescriptor(
            name="request_data",
            full_name="Request.request_data",
            index=1,
            number=2,
            type=9,
            cpp_type=9,
            label=1,
            has_default_value=False,
            default_value=b"".decode("utf-8"),
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
    ],
    extensions=[],
    nested_types=[],
    enum_types=[],
    serialized_options=None,
    is_extendable=False,
    syntax="proto3",
    extension_ranges=[],
    oneofs=[],
    serialized_start=21,
    serialized_end=71,
)


_RESPONSE = _descriptor.Descriptor(
    name="Response",
    full_name="Response",
    filename=None,
    file=DESCRIPTOR,
    containing_type=None,
    fields=[
        _descriptor.FieldDescriptor(
            name="server_id",
            full_name="Response.server_id",
            index=0,
            number=1,
            type=3,
            cpp_type=2,
            label=1,
            has_default_value=False,
            default_value=0,
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
        _descriptor.FieldDescriptor(
            name="response_data",
            full_name="Response.response_data",
            index=1,
            number=2,
            type=9,
            cpp_type=9,
            label=1,
            has_default_value=False,
            default_value=b"".decode("utf-8"),
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
    ],
    extensions=[],
    nested_types=[],
    enum_types=[],
    serialized_options=None,
    is_extendable=False,
    syntax="proto3",
    extension_ranges=[],
    oneofs=[],
    serialized_start=73,
    serialized_end=125,
)

DESCRIPTOR.message_types_by_name["Request"] = _REQUEST
DESCRIPTOR.message_types_by_name["Response"] = _RESPONSE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

Request = _reflection.GeneratedProtocolMessageType(
    "Request",
    (_message.Message,),
    {
        "DESCRIPTOR": _REQUEST,
        "__module__": "test_server_pb2"
        # @@protoc_insertion_point(class_scope:Request)
    },
)
_sym_db.RegisterMessage(Request)

Response = _reflection.GeneratedProtocolMessageType(
    "Response",
    (_message.Message,),
    {
        "DESCRIPTOR": _RESPONSE,
        "__module__": "test_server_pb2"
        # @@protoc_insertion_point(class_scope:Response)
    },
)
_sym_db.RegisterMessage(Response)


_GRPCTESTSERVER = _descriptor.ServiceDescriptor(
    name="GRPCTestServer",
    full_name="GRPCTestServer",
    file=DESCRIPTOR,
    index=0,
    serialized_options=None,
    serialized_start=128,
    serialized_end=334,
    methods=[
        _descriptor.MethodDescriptor(
            name="SimpleMethod",
            full_name="GRPCTestServer.SimpleMethod",
            index=0,
            containing_service=None,
            input_type=_REQUEST,
            output_type=_RESPONSE,
            serialized_options=None,
        ),
        _descriptor.MethodDescriptor(
            name="ClientStreamingMethod",
            full_name="GRPCTestServer.ClientStreamingMethod",
            index=1,
            containing_service=None,
            input_type=_REQUEST,
            output_type=_RESPONSE,
            serialized_options=None,
        ),
        _descriptor.MethodDescriptor(
            name="ServerStreamingMethod",
            full_name="GRPCTestServer.ServerStreamingMethod",
            index=2,
            containing_service=None,
            input_type=_REQUEST,
            output_type=_RESPONSE,
            serialized_options=None,
        ),
        _descriptor.MethodDescriptor(
            name="BidirectionalStreamingMethod",
            full_name="GRPCTestServer.BidirectionalStreamingMethod",
            index=3,
            containing_service=None,
            input_type=_REQUEST,
            output_type=_RESPONSE,
            serialized_options=None,
        ),
    ],
)
_sym_db.RegisterServiceDescriptor(_GRPCTESTSERVER)

DESCRIPTOR.services_by_name["GRPCTestServer"] = _GRPCTESTSERVER

# @@protoc_insertion_point(module_scope)
