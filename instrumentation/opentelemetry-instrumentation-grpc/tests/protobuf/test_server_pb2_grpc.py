# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
import grpc
from tests.protobuf import test_server_pb2 as test__server__pb2


class GRPCTestServerStub:
    """Missing associated documentation comment in .proto file"""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.SimpleMethod = channel.unary_unary(
            "/GRPCTestServer/SimpleMethod",
            request_serializer=test__server__pb2.Request.SerializeToString,
            response_deserializer=test__server__pb2.Response.FromString,
        )
        self.ClientStreamingMethod = channel.stream_unary(
            "/GRPCTestServer/ClientStreamingMethod",
            request_serializer=test__server__pb2.Request.SerializeToString,
            response_deserializer=test__server__pb2.Response.FromString,
        )
        self.ServerStreamingMethod = channel.unary_stream(
            "/GRPCTestServer/ServerStreamingMethod",
            request_serializer=test__server__pb2.Request.SerializeToString,
            response_deserializer=test__server__pb2.Response.FromString,
        )
        self.BidirectionalStreamingMethod = channel.stream_stream(
            "/GRPCTestServer/BidirectionalStreamingMethod",
            request_serializer=test__server__pb2.Request.SerializeToString,
            response_deserializer=test__server__pb2.Response.FromString,
        )


class GRPCTestServerServicer:
    """Missing associated documentation comment in .proto file"""

    def SimpleMethod(self, request, context):
        """Missing associated documentation comment in .proto file"""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")

    def ClientStreamingMethod(self, request_iterator, context):
        """Missing associated documentation comment in .proto file"""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")

    def ServerStreamingMethod(self, request, context):
        """Missing associated documentation comment in .proto file"""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")

    def BidirectionalStreamingMethod(self, request_iterator, context):
        """Missing associated documentation comment in .proto file"""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")


def add_GRPCTestServerServicer_to_server(servicer, server):
    rpc_method_handlers = {
        "SimpleMethod": grpc.unary_unary_rpc_method_handler(
            servicer.SimpleMethod,
            request_deserializer=test__server__pb2.Request.FromString,
            response_serializer=test__server__pb2.Response.SerializeToString,
        ),
        "ClientStreamingMethod": grpc.stream_unary_rpc_method_handler(
            servicer.ClientStreamingMethod,
            request_deserializer=test__server__pb2.Request.FromString,
            response_serializer=test__server__pb2.Response.SerializeToString,
        ),
        "ServerStreamingMethod": grpc.unary_stream_rpc_method_handler(
            servicer.ServerStreamingMethod,
            request_deserializer=test__server__pb2.Request.FromString,
            response_serializer=test__server__pb2.Response.SerializeToString,
        ),
        "BidirectionalStreamingMethod": grpc.stream_stream_rpc_method_handler(
            servicer.BidirectionalStreamingMethod,
            request_deserializer=test__server__pb2.Request.FromString,
            response_serializer=test__server__pb2.Response.SerializeToString,
        ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
        "GRPCTestServer", rpc_method_handlers
    )
    server.add_generic_rpc_handlers((generic_handler,))


# This class is part of an EXPERIMENTAL API.
class GRPCTestServer:
    """Missing associated documentation comment in .proto file"""

    @staticmethod
    def SimpleMethod(
        request,
        target,
        options=(),
        channel_credentials=None,
        call_credentials=None,
        compression=None,
        wait_for_ready=None,
        timeout=None,
        metadata=None,
    ):
        return grpc.experimental.unary_unary(
            request,
            target,
            "/GRPCTestServer/SimpleMethod",
            test__server__pb2.Request.SerializeToString,
            test__server__pb2.Response.FromString,
            options,
            channel_credentials,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
        )

    @staticmethod
    def ClientStreamingMethod(
        request_iterator,
        target,
        options=(),
        channel_credentials=None,
        call_credentials=None,
        compression=None,
        wait_for_ready=None,
        timeout=None,
        metadata=None,
    ):
        return grpc.experimental.stream_unary(
            request_iterator,
            target,
            "/GRPCTestServer/ClientStreamingMethod",
            test__server__pb2.Request.SerializeToString,
            test__server__pb2.Response.FromString,
            options,
            channel_credentials,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
        )

    @staticmethod
    def ServerStreamingMethod(
        request,
        target,
        options=(),
        channel_credentials=None,
        call_credentials=None,
        compression=None,
        wait_for_ready=None,
        timeout=None,
        metadata=None,
    ):
        return grpc.experimental.unary_stream(
            request,
            target,
            "/GRPCTestServer/ServerStreamingMethod",
            test__server__pb2.Request.SerializeToString,
            test__server__pb2.Response.FromString,
            options,
            channel_credentials,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
        )

    @staticmethod
    def BidirectionalStreamingMethod(
        request_iterator,
        target,
        options=(),
        channel_credentials=None,
        call_credentials=None,
        compression=None,
        wait_for_ready=None,
        timeout=None,
        metadata=None,
    ):
        return grpc.experimental.stream_stream(
            request_iterator,
            target,
            "/GRPCTestServer/BidirectionalStreamingMethod",
            test__server__pb2.Request.SerializeToString,
            test__server__pb2.Response.FromString,
            options,
            channel_credentials,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
        )
