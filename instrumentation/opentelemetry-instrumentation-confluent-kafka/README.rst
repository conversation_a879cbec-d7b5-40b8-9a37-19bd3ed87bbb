OpenTelemetry confluent-kafka Instrumentation
=============================================

|pypi|

.. |pypi| image:: https://badge.fury.io/py/opentelemetry-instrumentation-confluent-kafka.svg
   :target: https://pypi.org/project/opentelemetry-instrumentation-confluent-kafka/

This library allows tracing requests made by the confluent-kafka library.

Installation
------------

::

    pip install opentelemetry-instrumentation-confluent-kafka


References
----------

* `OpenTelemetry confluent-kafka/ Tracing <https://opentelemetry-python-contrib.readthedocs.io/en/latest/instrumentation/confluent-kafka/confluent-kafka.html>`_
* `OpenTelemetry Project <https://opentelemetry.io/>`_
