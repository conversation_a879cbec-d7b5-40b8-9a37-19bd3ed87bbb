[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "opentelemetry-instrumentation-confluent-kafka"
dynamic = ["version"]
description = "OpenTelemetry Confluent Kafka instrumentation"
readme = "README.rst"
license = "Apache-2.0"
requires-python = ">=3.8"
authors = [
  { name = "OpenTelemetry Authors", email = "<EMAIL>" },
]
classifiers = [
  "Development Status :: 4 - Beta",
  "Intended Audience :: Developers",
  "License :: OSI Approved :: Apache Software License",
  "Programming Language :: Python",
  "Programming Language :: Python :: 3",
  "Programming Language :: Python :: 3.8",
]
dependencies = [
  "opentelemetry-instrumentation == 0.45b0",
  "opentelemetry-api ~= 1.12",
  "wrapt >= 1.0.0, < 2.0.0",
]

[project.optional-dependencies]
instruments = [
  "confluent-kafka >= 1.8.2, <= 2.3.0",
]

[project.entry-points.opentelemetry_instrumentor]
confluent_kafka = "opentelemetry.instrumentation.confluent_kafka:ConfluentKafkaInstrumentor"

[project.urls]
Homepage = "https://github.com/open-telemetry/opentelemetry-python-contrib/tree/main/instrumentation/opentelemetry-instrumentation-confluent-kafka"

[tool.hatch.version]
path = "src/opentelemetry/instrumentation/confluent_kafka/version.py"

[tool.hatch.build.targets.sdist]
include = [
  "/src",
  "/tests",
]

[tool.hatch.build.targets.wheel]
packages = ["src/opentelemetry"]
