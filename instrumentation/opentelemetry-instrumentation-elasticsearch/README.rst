OpenTelemetry elasticsearch Integration
========================================

|pypi|

.. |pypi| image:: https://badge.fury.io/py/opentelemetry-instrumentation-elasticsearch.svg
   :target: https://pypi.org/project/opentelemetry-instrumentation-elasticsearch/

This library allows tracing elasticsearch made by the
`elasticsearch <https://elasticsearch-py.readthedocs.io/en/master/>`_ library.

Installation
------------

::

     pip install opentelemetry-instrumentation-elasticsearch

References
----------

* `OpenTelemetry elasticsearch Integration <https://opentelemetry-python-contrib.readthedocs.io/en/latest/instrumentation/elasticsearch/elasticsearch.html>`_
* `OpenTelemetry Project <https://opentelemetry.io/>`_
* `OpenTelemetry Python Examples <https://github.com/open-telemetry/opentelemetry-python/tree/main/docs/examples>`_
