OpenTelemetry Boto3 SQS Instrumentation
=======================================

|pypi|

.. |pypi| image:: https://badge.fury.io/py/opentelemetry-instrumentation-boto3sqs.svg
   :target: https://pypi.org/project/opentelemetry-instrumentation-boto3sqs/

This library allows tracing requests made by the Boto3 library to the SQS service.

Installation
------------

::

    pip install opentelemetry-instrumentation-boto3sqs


References
----------

* `OpenTelemetry boto3sqs/ Tracing <https://opentelemetry-python-contrib.readthedocs.io/en/latest/instrumentation/boto3sqs/boto3sqs.html>`_
* `OpenTelemetry Project <https://opentelemetry.io/>`_
