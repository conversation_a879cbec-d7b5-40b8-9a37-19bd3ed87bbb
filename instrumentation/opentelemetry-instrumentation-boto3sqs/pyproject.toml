[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "opentelemetry-instrumentation-boto3sqs"
dynamic = ["version"]
description = "Boto3 SQS service tracing for OpenTelemetry"
readme = "README.rst"
license = "Apache-2.0"
requires-python = ">=3.8"
authors = [
  { name = "OpenTelemetry Authors", email = "<EMAIL>" },
]
classifiers = [
  "Development Status :: 4 - Beta",
  "Intended Audience :: Developers",
  "License :: OSI Approved :: Apache Software License",
  "Programming Language :: Python",
  "Programming Language :: Python :: 3",
  "Programming Language :: Python :: 3.8",
  "Programming Language :: Python :: 3.9",
  "Programming Language :: Python :: 3.10",
  "Programming Language :: Python :: 3.11",
]
dependencies = [
  "opentelemetry-api ~= 1.12",
  "opentelemetry-instrumentation == 0.45b0",
  "opentelemetry-semantic-conventions == 0.45b0",
  "wrapt >= 1.0.0, < 2.0.0",
]

[project.optional-dependencies]
instruments = [
  "boto3 ~= 1.0",
]

[project.entry-points.opentelemetry_instrumentor]
boto3 = "opentelemetry.instrumentation.boto3sqs:Boto3SQSInstrumentor"

[project.urls]
Homepage = "https://github.com/open-telemetry/opentelemetry-python-contrib/tree/main/instrumentation/opentelemetry-instrumentation-boto3sqs"

[tool.hatch.version]
path = "src/opentelemetry/instrumentation/boto3sqs/version.py"

[tool.hatch.build.targets.sdist]
include = [
  "/src",
  "/tests",
]

[tool.hatch.build.targets.wheel]
packages = ["src/opentelemetry"]
