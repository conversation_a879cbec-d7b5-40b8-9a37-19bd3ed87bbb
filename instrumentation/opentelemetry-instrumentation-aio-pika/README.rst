OpenTelemetry Aio-pika Instrumentation
======================================

|pypi|

.. |pypi| image:: https://badge.fury.io/py/opentelemetry-instrumentation-aio-pika.svg
   :target: https://pypi.org/project/opentelemetry-instrumentation-aio-pika/

This library allows tracing requests made by the Aio-pika library.

Installation
------------

::

    pip install opentelemetry-instrumentation-aio-pika

References
----------

* `OpenTelemetry Aio-pika instrumentation <https://opentelemetry-python-contrib.readthedocs.io/en/latest/instrumentation/aio_pika/aio_pika.html>`_
* `OpenTelemetry Project <https://opentelemetry.io/>`_
* `OpenTelemetry Python Examples <https://github.com/open-telemetry/opentelemetry-python/tree/main/docs/examples>`_
