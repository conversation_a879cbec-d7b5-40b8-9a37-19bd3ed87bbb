OpenTelemetry asyncpg Instrumentation
=====================================

|pypi|

.. |pypi| image:: https://badge.fury.io/py/opentelemetry-instrumentation-asyncpg.svg
   :target: https://pypi.org/project/opentelemetry-instrumentation-asyncpg/

This library allows tracing PostgreSQL queries made by the
`asyncpg <https://magicstack.github.io/asyncpg/current/>`_ library.

Installation
------------

::

     pip install opentelemetry-instrumentation-asyncpg

References
----------

* `OpenTelemetry asyncpg Instrumentation <https://opentelemetry-python-contrib.readthedocs.io/en/latest/instrumentation/asyncpg/asyncpg.html>`_
* `OpenTelemetry Project <https://opentelemetry.io/>`_
* `OpenTelemetry Python Examples <https://github.com/open-telemetry/opentelemetry-python/tree/main/docs/examples>`_
