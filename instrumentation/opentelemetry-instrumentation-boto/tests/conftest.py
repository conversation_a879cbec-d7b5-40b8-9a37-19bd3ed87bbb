# Copyright The OpenTelemetry Authors
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

from os import environ


def pytest_sessionstart(session):
    # pylint: disable=unused-argument
    environ["AWS_ACCESS_KEY_ID"] = "testing"
    environ["AWS_SECRET_ACCESS_KEY"] = "testing"
    environ["AWS_SECURITY_TOKEN"] = "testing"
    environ["AWS_SESSION_TOKEN"] = "testing"


def pytest_sessionfinish(session):
    # pylint: disable=unused-argument
    environ.pop("AWS_ACCESS_KEY_ID")
    environ.pop("AWS_SECRET_ACCESS_KEY")
    environ.pop("AWS_SECURITY_TOKEN")
    environ.pop("AWS_SESSION_TOKEN")
