OpenTelemetry Boto Tracing
==========================

|pypi|

.. |pypi| image:: https://badge.fury.io/py/opentelemetry-instrumentation-boto.svg
   :target: https://pypi.org/project/opentelemetry-instrumentation-boto/

This library allows tracing requests made by the Boto library.

Installation
------------

::

    pip install opentelemetry-instrumentation-boto


References
----------

* `OpenTelemetry Boto Tracing <https://opentelemetry-python-contrib.readthedocs.io/en/latest/instrumentation/boto/boto.html>`_
* `OpenTelemetry Project <https://opentelemetry.io/>`_
* `OpenTelemetry Python Examples <https://github.com/open-telemetry/opentelemetry-python/tree/main/docs/examples>`_
