OpenTelemetry Tornado Instrumentation
======================================

|pypi|

.. |pypi| image:: https://badge.fury.io/py/opentelemetry-instrumentation-tornado.svg
   :target: https://pypi.org/project/opentelemetry-instrumentation-tornado/

This library builds on the OpenTelemetry WSGI middleware to track web requests
in Tornado applications.

Installation
------------

::

    pip install opentelemetry-instrumentation-tornado

References
----------

* `OpenTelemetry Tornado Instrumentation <https://opentelemetry-python-contrib.readthedocs.io/en/latest/instrumentation/tornado/tornado.html>`_
* `OpenTelemetry Project <https://opentelemetry.io/>`_
* `OpenTelemetry Python Examples <https://github.com/open-telemetry/opentelemetry-python/tree/main/docs/examples>`_
