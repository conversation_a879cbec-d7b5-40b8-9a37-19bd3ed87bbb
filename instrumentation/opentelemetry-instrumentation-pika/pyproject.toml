[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "opentelemetry-instrumentation-pika"
dynamic = ["version"]
description = "OpenTelemetry pika instrumentation"
readme = "README.rst"
license = "Apache-2.0"
requires-python = ">=3.8"
authors = [
  { name = "OpenTelemetry Authors", email = "<EMAIL>" },
]
classifiers = [
  "Development Status :: 4 - Beta",
  "Intended Audience :: Developers",
  "License :: OSI Approved :: Apache Software License",
  "Programming Language :: Python",
  "Programming Language :: Python :: 3",
  "Programming Language :: Python :: 3.8",
  "Programming Language :: Python :: 3.9",
  "Programming Language :: Python :: 3.10",
  "Programming Language :: Python :: 3.11",
]
dependencies = [
  "opentelemetry-instrumentation == 0.45b0",
  "opentelemetry-api ~= 1.5",
  "packaging >= 20.0",
  "wrapt >= 1.0.0, < 2.0.0",
]

[project.optional-dependencies]
instruments = [
  "pika >= 0.12.0",
]

[project.entry-points.opentelemetry_instrumentor]
pika = "opentelemetry.instrumentation.pika:PikaInstrumentor"

[project.urls]
Homepage = "https://github.com/open-telemetry/opentelemetry-python-contrib/tree/main/instrumentation/opentelemetry-instrumentation-pika"

[tool.hatch.version]
path = "src/opentelemetry/instrumentation/pika/version.py"

[tool.hatch.build.targets.sdist]
include = [
  "/src",
  "/tests",
]

[tool.hatch.build.targets.wheel]
packages = ["src/opentelemetry"]
