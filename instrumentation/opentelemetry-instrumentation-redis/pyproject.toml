[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "opentelemetry-instrumentation-redis"
dynamic = ["version"]
description = "OpenTelemetry Redis instrumentation"
readme = "README.rst"
license = "Apache-2.0"
requires-python = ">=3.8"
authors = [
  { name = "OpenTelemetry Authors", email = "<EMAIL>" },
]
classifiers = [
  "Development Status :: 4 - Beta",
  "Intended Audience :: Developers",
  "License :: OSI Approved :: Apache Software License",
  "Programming Language :: Python",
  "Programming Language :: Python :: 3",
  "Programming Language :: Python :: 3.8",
  "Programming Language :: Python :: 3.9",
  "Programming Language :: Python :: 3.10",
  "Programming Language :: Python :: 3.11",
]
dependencies = [
  "opentelemetry-api ~= 1.12",
  "opentelemetry-instrumentation == 0.45b0",
  "opentelemetry-semantic-conventions == 0.45b0",
  "wrapt >= 1.12.1",
]

[project.optional-dependencies]
instruments = [
  "redis >= 2.6",
]

[project.entry-points.opentelemetry_instrumentor]
redis = "opentelemetry.instrumentation.redis:RedisInstrumentor"

[project.urls]
Homepage = "https://github.com/open-telemetry/opentelemetry-python-contrib/tree/main/instrumentation/opentelemetry-instrumentation-redis"

[tool.hatch.version]
path = "src/opentelemetry/instrumentation/redis/version.py"

[tool.hatch.build.targets.sdist]
include = [
  "/src",
  "/tests",
]

[tool.hatch.build.targets.wheel]
packages = ["src/opentelemetry"]
