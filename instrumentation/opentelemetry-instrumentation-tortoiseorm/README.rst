OpenTelemetry Tortoise ORM Instrumentation
==========================================

|pypi|

.. |pypi| image:: https://badge.fury.io/py/opentelemetry-instrumentation-tortoiseorm.svg
   :target: https://pypi.org/project/opentelemetry-instrumentation-tortoiseorm/

This library allows tracing queries made by tortoise ORM backends, mysql, postgres and sqlite.

Installation
------------

::

     pip install opentelemetry-instrumentation-tortoiseorm

References
----------

* `OpenTelemetry Project <https://opentelemetry.io/>`_
* `Tortoise ORM <https://tortoise.github.io/>`_
* `OpenTelemetry Python Examples <https://github.com/open-telemetry/opentelemetry-python/tree/main/docs/examples>`_
