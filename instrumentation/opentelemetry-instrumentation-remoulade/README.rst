OpenTelemetry Remoulade Instrumentation
=======================================

|pypi|

.. |pypi| image:: https://badge.fury.io/py/opentelemetry-instrumentation-remoulade.svg
   :target: https://pypi.org/project/opentelemetry-instrumentation-remoulade/

This library allows tracing requests made by the Remoulade library.

Installation
------------

::

    pip install opentelemetry-instrumentation-remoulade

References
----------

* `OpenTelemetry Remoulade Instrumentation <https://opentelemetry-python-contrib.readthedocs.io/en/latest/instrumentation/remoulade/remoulade.html>`_
* `OpenTelemetry Project <https://opentelemetry.io/>`_
* `OpenTelemetry Python Examples <https://github.com/open-telemetry/opentelemetry-python/tree/main/docs/examples>`_
