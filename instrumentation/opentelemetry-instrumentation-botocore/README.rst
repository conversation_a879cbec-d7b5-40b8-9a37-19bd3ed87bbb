OpenTelemetry Botocore Tracing
==============================

|pypi|

.. |pypi| image:: https://badge.fury.io/py/opentelemetry-instrumentation-botocore.svg
   :target: https://pypi.org/project/opentelemetry-instrumentation-botocore/

This library allows tracing requests made by the Botocore library.

Installation
------------

::

    pip install opentelemetry-instrumentation-botocore


References
----------

* `OpenTelemetry Botocore Tracing <https://opentelemetry-python-contrib.readthedocs.io/en/latest/instrumentation/botocore/botocore.html>`_
* `OpenTelemetry Project <https://opentelemetry.io/>`_
* `OpenTelemetry Python Examples <https://github.com/open-telemetry/opentelemetry-python/tree/main/docs/examples>`_
