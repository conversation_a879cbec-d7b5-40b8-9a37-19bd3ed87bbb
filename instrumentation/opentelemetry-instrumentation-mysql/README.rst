OpenTelemetry MySQL Instrumentation
===================================

|pypi|

.. |pypi| image:: https://badge.fury.io/py/opentelemetry-instrumentation-mysql.svg
   :target: https://pypi.org/project/opentelemetry-instrumentation-mysql/

Instrumentation with MySQL that supports the mysql-connector library and is
specified to trace_integration using 'MySQL'.


Installation
------------

::

    pip install opentelemetry-instrumentation-mysql


References
----------
* `OpenTelemetry MySQL Instrumentation <https://opentelemetry-python-contrib.readthedocs.io/en/latest/instrumentation/mysql/mysql.html>`_
* `OpenTelemetry Project <https://opentelemetry.io/>`_
* `OpenTelemetry Python Examples <https://github.com/open-telemetry/opentelemetry-python/tree/main/docs/examples>`_

