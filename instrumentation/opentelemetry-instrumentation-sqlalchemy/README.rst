OpenTelemetry SQLAlchemy Instrumentation
========================================

|pypi|

.. |pypi| image:: https://badge.fury.io/py/opentelemetry-instrumentation-sqlalchemy.svg
   :target: https://pypi.org/project/opentelemetry-instrumentation-sqlalchemy/

This library allows tracing requests made by the SQLAlchemy library.

Installation
------------

::

    pip install opentelemetry-instrumentation-sqlalchemy


References
----------

* `SQLAlchemy Project <https://www.sqlalchemy.org/>`_
* `OpenTelemetry SQLAlchemy Tracing <https://opentelemetry-python-contrib.readthedocs.io/en/latest/instrumentation/sqlalchemy/sqlalchemy.html>`_
* `OpenTelemetry Project <https://opentelemetry.io/>`_
* `OpenTelemetry Python Examples <https://github.com/open-telemetry/opentelemetry-python/tree/main/docs/examples>`_