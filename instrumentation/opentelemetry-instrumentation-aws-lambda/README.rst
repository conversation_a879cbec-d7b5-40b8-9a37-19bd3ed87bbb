OpenTelemetry AWS Lambda Tracing
================================

|pypi|

.. |pypi| image:: https://badge.fury.io/py/opentelemetry-instrumentation-aws-lambda.svg
   :target: https://pypi.org/project/opentelemetry-instrumentation-aws-lambda/

This library provides an Instrumentor used to trace requests made by the Lambda
functions on the AWS Lambda service.

It also provides scripts used by AWS Lambda Layers to automatically initialize
the OpenTelemetry SDK. Learn more on the AWS Distro for OpenTelemetry (ADOT)
`documentation for the Python Lambda Layer <https://aws-otel.github.io/docs/getting-started/lambda/lambda-python>`_.

Installation
------------

::

    pip install opentelemetry-instrumentation-aws-lambda


References
----------

* `OpenTelemetry AWS Lambda Tracing <https://opentelemetry-python-contrib.readthedocs.io/en/latest/instrumentation/aws_lambda/aws_lambda.html>`_
* `OpenTelemetry Project <https://opentelemetry.io/>`_
* `OpenTelemetry Python Examples <https://github.com/open-telemetry/opentelemetry-python/tree/main/docs/examples>`_
* `ADOT Python Lambda Layer Documentation <https://aws-otel.github.io/docs/getting-started/lambda/lambda-python>`_
