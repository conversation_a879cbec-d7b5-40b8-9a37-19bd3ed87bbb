[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "opentelemetry-instrumentation-aws-lambda"
dynamic = ["version"]
description = "OpenTelemetry AWS Lambda instrumentation"
readme = "README.rst"
license = "Apache-2.0"
requires-python = ">=3.8"
authors = [
  { name = "OpenTelemetry Authors", email = "<EMAIL>" },
]
classifiers = [
  "Development Status :: 4 - Beta",
  "Intended Audience :: Developers",
  "License :: OSI Approved :: Apache Software License",
  "Programming Language :: Python",
  "Programming Language :: Python :: 3",
  "Programming Language :: Python :: 3.8",
]
dependencies = [
  "opentelemetry-instrumentation == 0.45b0",
  "opentelemetry-propagator-aws-xray == 1.0.1",
  "opentelemetry-semantic-conventions == 0.45b0",
]

[project.optional-dependencies]
instruments = []

[project.urls]
Homepage = "https://github.com/open-telemetry/opentelemetry-python-contrib/tree/main/instrumentation/opentelemetry-instrumentation-aws-lambda"

[tool.hatch.version]
path = "src/opentelemetry/instrumentation/aws_lambda/version.py"

[tool.hatch.build.targets.sdist]
include = [
  "/src",
  "/tests",
]

[tool.hatch.build.targets.wheel]
packages = ["src/opentelemetry"]
