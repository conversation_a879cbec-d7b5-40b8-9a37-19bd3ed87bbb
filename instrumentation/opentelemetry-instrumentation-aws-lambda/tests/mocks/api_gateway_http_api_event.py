# Generated via `sam local generate-event apigateway http-api-proxy`

MOCK_LAMBDA_API_GATEWAY_HTTP_API_EVENT = {
    "version": "2.0",
    "routeKey": "$default",
    "rawPath": "/path/to/resource",
    "rawQueryString": "parameter1=value1&parameter1=value2&parameter2=value",
    "cookies": ["cookie1", "cookie2"],
    "headers": {"header1": "value1", "Header2": "value1,value2"},
    "queryStringParameters": {
        "parameter1": "value1,value2",
        "parameter2": "value",
    },
    "requestContext": {
        "accountId": "************",
        "apiId": "api-id",
        "authentication": {
            "clientCert": {
                "clientCertPem": "CERT_CONTENT",
                "subjectDN": "www.example.com",
                "issuerDN": "Example issuer",
                "serialNumber": "a1:a1:a1:a1:a1:a1:a1:a1:a1:a1:a1:a1:a1:a1:a1:a1",
                "validity": {
                    "notBefore": "May 28 12:30:02 2019 GMT",
                    "notAfter": "Aug  5 09:36:04 2021 GMT",
                },
            }
        },
        "authorizer": {
            "jwt": {
                "claims": {"claim1": "value1", "claim2": "value2"},
                "scopes": ["scope1", "scope2"],
            }
        },
        "domainName": "id.execute-api.us-east-1.amazonaws.com",
        "domainPrefix": "id",
        "http": {
            "method": "POST",
            "path": "/path/to/resource",
            "protocol": "HTTP/1.1",
            "sourceIp": "***********/32",
            "userAgent": "agent",
        },
        "requestId": "id",
        "routeKey": "$default",
        "stage": "$default",
        "time": "12/Mar/2020:19:03:58 +0000",
        "timeEpoch": 1583348638390,
    },
    "body": "eyJ0ZXN0IjoiYm9keSJ9",
    "pathParameters": {"parameter1": "value1"},
    "isBase64Encoded": True,
    "stageVariables": {"stageVariable1": "value1", "stageVariable2": "value2"},
}
