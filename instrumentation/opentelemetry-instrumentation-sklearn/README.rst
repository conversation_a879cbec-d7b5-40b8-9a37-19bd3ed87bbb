OpenTelemetry Scikit-Learn Instrumentation
==========================================

|pypi|

.. |pypi| image:: https://badge.fury.io/py/opentelemetry-instrumentation-sklearn.svg
   :target: https://pypi.org/project/opentelemetry-instrumentation-sklearn/

This library allows tracing HTTP requests made by the
`scikit-learn <https://scikit-learn.org/stable/>`_ library.

Installation
------------

::

     pip install opentelemetry-instrumentation-sklearn

References
----------

* `OpenTelemetry sklearn Instrumentation <https://opentelemetry-python-contrib.readthedocs.io/en/latest/instrumentation/sklearn/sklearn.html>`_
* `OpenTelemetry Project <https://opentelemetry.io/>`_
* `OpenTelemetry Python Examples <https://github.com/open-telemetry/opentelemetry-python/tree/main/docs/examples>`_
