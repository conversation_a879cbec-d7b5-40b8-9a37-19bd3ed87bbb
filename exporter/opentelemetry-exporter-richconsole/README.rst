OpenTelemetry Rich Console Exporter
===================================

|pypi|

.. |pypi| image:: https://badge.fury.io/py/opentelemetry-exporter-richconsole.svg
   :target: https://pypi.org/project/opentelemetry-exporter-richconsole/

This library is a console exporter using the Rich tree view. When used with a batch span processor, the rich console exporter will show the trace as a 
tree and all related spans as children within the tree, including properties.

Installation
------------

::

    pip install opentelemetry-exporter-richconsole


.. _Rich: https://rich.readthedocs.io/
.. _OpenTelemetry: https://github.com/open-telemetry/opentelemetry-python/


References
----------

* `Rich <https://rich.readthedocs.io/>`_
* `OpenTelemetry Project <https://opentelemetry.io/>`_
