[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "opentelemetry-exporter-richconsole"
dynamic = ["version"]
description = "Rich Console Exporter for OpenTelemetry"
readme = "README.rst"
license = "Apache-2.0"
requires-python = ">=3.8"
authors = [
  { name = "OpenTelemetry Authors", email = "<EMAIL>" },
]
classifiers = [
  "Development Status :: 4 - Beta",
  "Intended Audience :: Developers",
  "License :: OSI Approved :: Apache Software License",
  "Programming Language :: Python",
  "Programming Language :: Python :: 3",
  "Programming Language :: Python :: 3.8",
  "Programming Language :: Python :: 3.9",
  "Programming Language :: Python :: 3.10",
  "Programming Language :: Python :: 3.11",
]
dependencies = [
  "opentelemetry-api ~= 1.12",
  "opentelemetry-sdk ~= 1.12",
  "opentelemetry-semantic-conventions == 0.45b0",
  "rich>=10.0.0",
]

[project.urls]
Homepage = "https://github.com/open-telemetry/opentelemetry-python-contrib/tree/main/exporter/opentelemetry-exporter-richconsole"

[tool.hatch.version]
path = "src/opentelemetry/exporter/richconsole/version.py"

[tool.hatch.build.targets.sdist]
include = [
  "/src",
  "/tests",
]

[tool.hatch.build.targets.wheel]
packages = ["src/opentelemetry"]
