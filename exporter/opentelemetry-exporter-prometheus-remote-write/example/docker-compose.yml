# Copyright The OpenTelemetry Authors
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

version: "3.8"

services:
  cortex:
    image: quay.io/cortexproject/cortex:v1.5.0
    command:
      - -config.file=./config/cortex-config.yml
    volumes:
      - ./cortex-config.yml:/config/cortex-config.yml:ro
    ports:
      - 9009:9009
  grafana:
    image: grafana/grafana:latest
    ports:
      - 3000:3000
  sample_app:
    build:
      context: ../
      dockerfile: ./example/Dockerfile
