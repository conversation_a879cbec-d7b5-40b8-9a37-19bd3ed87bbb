[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"


[project]
name = "opentelemetry-exporter-prometheus-remote-write"
dynamic = ["version"]
description = "Prometheus Remote Write Metrics Exporter for OpenTelemetry"
readme = "README.rst"
license = "Apache-2.0"
requires-python = ">=3.8"
authors = [
  { name = "OpenTelemetry Authors", email = "<EMAIL>" },
]
classifiers = [
  "Development Status :: 4 - Beta",
  "Intended Audience :: Developers",
  "License :: OSI Approved :: Apache Software License",
  "Programming Language :: Python",
  "Programming Language :: Python :: 3",
  "Programming Language :: Python :: 3.8",
  "Programming Language :: Python :: 3.9",
  "Programming Language :: Python :: 3.10",
]
dependencies = [
  "protobuf ~= 4.21",
  "requests ~= 2.28",
  "opentelemetry-api ~= 1.12",
  "opentelemetry-sdk ~= 1.12",
  "python-snappy ~= 0.6",
]

[project.urls]
Homepage = "https://github.com/open-telemetry/opentelemetry-python-contrib/tree/main/exporter/opentelemetry-exporter-prometheus-remote-write"

[tool.hatch.version]
path = "src/opentelemetry/exporter/prometheus_remote_write/version.py"

[tool.hatch.build.targets.sdist]
include = [
  "/src",
  "/tests",
]

[tool.hatch.build.targets.wheel]
packages = ["src/opentelemetry"]
